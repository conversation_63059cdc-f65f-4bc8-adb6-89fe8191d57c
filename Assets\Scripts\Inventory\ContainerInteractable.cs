using UnityEngine;

/// <summary>
/// Makes an object interactable as a container that can be opened with E key
/// </summary>
public class ContainerInteractable : MonoBehaviour
{
    [Header("Interaction Settings")]
    [Tooltip("Range within which player can interact with this container")]
    public float interactionRange = 2f;
    
    [Head<PERSON>("Container References")]
    [Toolt<PERSON>("The container inventory component")]
    public ContainerInventory containerInventory;
    
    [<PERSON><PERSON>("Visual Feedback")]
    [Tooltip("Optional sprite renderer for visual feedback")]
    public SpriteRenderer spriteRenderer;
    
    [Toolt<PERSON>("Color to tint the container when it's interactable")]
    public Color highlightColor = Color.white;
    
    private Color originalColor;
    private bool isHighlighted = false;
    private DualInventoryUI dualInventoryUI;
    
    private void Start()
    {
        // Get container inventory if not assigned
        if (containerInventory == null)
        {
            containerInventory = GetComponent<ContainerInventory>();
        }
        
        // Get sprite renderer if not assigned
        if (spriteRenderer == null)
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
        }
        
        // Store original color
        if (spriteRenderer != null)
        {
            originalColor = spriteRenderer.color;
        }
        
        // Find the dual inventory UI in the scene
        FindDualInventoryUI();
    }

    private void FindDualInventoryUI()
    {
        // Try to find DualInventoryUI component first
        dualInventoryUI = FindObjectOfType<DualInventoryUI>();

        if (dualInventoryUI == null)
        {
            Debug.Log("DualInventoryUI component not found, looking for PlayerController...");

            // If not found, try to get it from PlayerController
            PlayerController playerController = FindObjectOfType<PlayerController>();
            if (playerController != null)
            {
                // PlayerController has GameObject dualInventoryUI, we need the component
                if (playerController.dualInventoryUI != null)
                {
                    dualInventoryUI = playerController.dualInventoryUI.GetComponent<DualInventoryUI>();
                    if (dualInventoryUI != null)
                    {
                        Debug.Log("Found DualInventoryUI component from PlayerController");
                    }
                    else
                    {
                        Debug.LogWarning("PlayerController has dualInventoryUI GameObject but no DualInventoryUI component");
                    }
                }
                else
                {
                    Debug.LogWarning("PlayerController.dualInventoryUI is null");
                }
            }
            else
            {
                Debug.LogError("PlayerController not found in scene!");
            }
        }

        if (dualInventoryUI == null)
        {
            Debug.LogWarning("No DualInventoryUI found in scene. Container interaction may not work properly.");
        }
    }

    /// <summary>
    /// Open this container's inventory alongside the player's inventory
    /// </summary>
    public void OpenContainer()
    {
        if (containerInventory == null)
        {
            Debug.LogError($"Container {name} has no ContainerInventory component!");
            return;
        }

        // Try to find DualInventoryUI again if it's null
        if (dualInventoryUI == null)
        {
            Debug.Log("DualInventoryUI is null, trying to find it again...");

            // Try finding by component type first
            dualInventoryUI = FindObjectOfType<DualInventoryUI>();
            Debug.Log($"FindObjectOfType result: {(dualInventoryUI != null ? "Found" : "Not found")}");

            // If not found, try finding by GameObject name and add component
            if (dualInventoryUI == null)
            {
                GameObject dualObject = GameObject.Find("DualInventoryPanel");
                if (dualObject != null)
                {
                    Debug.Log($"Found DualInventoryPanel GameObject: {dualObject.name}");
                    dualInventoryUI = dualObject.GetComponent<DualInventoryUI>();
                    if (dualInventoryUI == null)
                    {
                        Debug.Log("Adding DualInventoryUI component...");
                        dualInventoryUI = dualObject.AddComponent<DualInventoryUI>();
                        Debug.Log("Added DualInventoryUI component to DualInventoryPanel");

                        // Setup the component references
                        dualInventoryUI.SetupReferences();
                    }
                    Debug.Log("Found DualInventoryUI by GameObject name");
                }
                else
                {
                    Debug.LogError("DualInventoryPanel GameObject not found!");
                }
            }
        }

        if (dualInventoryUI == null)
        {
            Debug.LogError("No DualInventoryUI found! Cannot open container.");
            return;
        }

        Debug.Log($"Opening container: {name}");
        dualInventoryUI.OpenDualInventory(containerInventory);
    }
    
    /// <summary>
    /// Show visual feedback that this container can be interacted with
    /// </summary>
    public void ShowInteractionFeedback()
    {
        if (isHighlighted || spriteRenderer == null) return;
        
        isHighlighted = true;
        spriteRenderer.color = highlightColor;
    }
    
    /// <summary>
    /// Hide visual feedback
    /// </summary>
    public void HideInteractionFeedback()
    {
        if (!isHighlighted || spriteRenderer == null) return;
        
        isHighlighted = false;
        spriteRenderer.color = originalColor;
    }
    
    /// <summary>
    /// Check if player is within interaction range
    /// </summary>
    public bool IsPlayerInRange(Transform playerTransform)
    {
        if (playerTransform == null) return false;
        
        float distance = Vector2.Distance(transform.position, playerTransform.position);
        return distance <= interactionRange;
    }
    
    private void OnDrawGizmosSelected()
    {
        // Draw interaction range in scene view
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, interactionRange);
    }
}

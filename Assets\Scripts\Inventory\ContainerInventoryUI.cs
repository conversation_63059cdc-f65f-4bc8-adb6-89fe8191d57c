using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

/// <summary>
/// UI controller for container inventories - similar to InventoryUI but for containers
/// </summary>
public class ContainerInventoryUI : MonoBehaviour, IPointerClickHandler
{
    [Header("References")]
    public ContainerInventory containerInventory;
    public GameObject slotPrefab;
    public GameObject itemUIPrefab;
    public Transform slotContainer;
    public Transform itemContainer;
    public Vector2 slotSize = new Vector2(64f, 64f);
    
    [Header("UI Elements")]
    public Text containerNameText;
    
    [Header("Grid Highlights")]
    [SerializeField] private Color validPlacementColor = new Color(0.3840584f, 0.6301887f, 0.47836998f, 0.2f);
    [SerializeField] private Color invalidPlacementColor = new Color(0.5999999f, 0.08037735f, 0.08037735f, 0.29411766f);
    
    // Private members
    private GridLayoutGroup gridLayout;
    private RectTransform gridRectTransform;
    private Vector2 gridOffset;
    private Dictionary<int, InventoryItemUI> itemUIMapping = new Dictionary<int, InventoryItemUI>();
    
    // Highlight system
    private GameObject highlightContainer;
    private GameObject[] cellHighlights;
    private bool highlightSystemInitialized = false;
    
    // Properties for external access
    public Vector2 GridOffset => gridOffset;
    public RectTransform GridRectTransform => gridRectTransform;
    public Vector2 GridSpacing => gridLayout != null ? gridLayout.spacing : Vector2.zero;
    
    private void Awake()
    {
        // Get grid rect transform
        if (slotContainer != null)
        {
            gridRectTransform = slotContainer.GetComponent<RectTransform>();
        }
    }
    
    /// <summary>
    /// Initialize the container UI with a specific container
    /// </summary>
    public void Initialize(ContainerInventory container)
    {
        containerInventory = container;
        
        if (containerInventory != null)
        {
            // Subscribe to inventory changes
            containerInventory.OnInventoryChanged += RefreshUI;
            
            // Update container name
            if (containerNameText != null)
            {
                containerNameText.text = containerInventory.GetContainerName();
            }
            
            // Generate grid and refresh UI
            GenerateGrid();
            InitializeHighlightSystem();
            RefreshUI();
        }
    }
    
    /// <summary>
    /// Clean up when container is closed
    /// </summary>
    public void Cleanup()
    {
        if (containerInventory != null)
        {
            containerInventory.OnInventoryChanged -= RefreshUI;
        }
        
        // Clear all item UIs
        foreach (var itemUI in itemUIMapping.Values)
        {
            if (itemUI != null)
            {
                Destroy(itemUI.gameObject);
            }
        }
        itemUIMapping.Clear();
    }
    
    /// <summary>
    /// Generate the grid slots
    /// </summary>
    private void GenerateGrid()
    {
        if (containerInventory == null || slotContainer == null) return;
        
        // Clear existing slots
        foreach (Transform child in slotContainer)
        {
            Destroy(child.gameObject);
        }
        
        // Configure grid layout
        gridLayout = slotContainer.GetComponent<GridLayoutGroup>();
        if (gridLayout == null)
        {
            gridLayout = slotContainer.gameObject.AddComponent<GridLayoutGroup>();
        }
        
        gridLayout.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
        gridLayout.constraintCount = containerInventory.width;
        gridLayout.cellSize = slotSize;
        
        // Create slots
        for (int y = 0; y < containerInventory.height; y++)
        {
            for (int x = 0; x < containerInventory.width; x++)
            {
                GameObject slotObj = Instantiate(slotPrefab, slotContainer);

                // Remove regular InventorySlot if it exists and add ContainerInventorySlot
                InventorySlot regularSlot = slotObj.GetComponent<InventorySlot>();
                if (regularSlot != null)
                {
                    Destroy(regularSlot);
                }

                ContainerInventorySlot containerSlot = slotObj.GetComponent<ContainerInventorySlot>();
                if (containerSlot == null)
                {
                    containerSlot = slotObj.AddComponent<ContainerInventorySlot>();
                }

                containerSlot.Initialize(x, y);
            }
        }
        
        // Calculate grid offset after layout
        Canvas.ForceUpdateCanvases();
        CalculateGridOffset();
    }
    
    /// <summary>
    /// Calculate the grid offset for positioning items
    /// </summary>
    private void CalculateGridOffset()
    {
        if (gridRectTransform != null)
        {
            Vector3[] corners = new Vector3[4];
            gridRectTransform.GetWorldCorners(corners);
            
            // Convert to local space
            Vector2 localTopLeft = transform.InverseTransformPoint(corners[1]);
            gridOffset = localTopLeft;
        }
    }
    
    /// <summary>
    /// Initialize the highlight system for drag and drop
    /// </summary>
    private void InitializeHighlightSystem()
    {
        if (highlightSystemInitialized || itemContainer == null) return;
        
        highlightContainer = new GameObject("ContainerGridHighlights");
        highlightContainer.transform.SetParent(itemContainer, false);
        highlightContainer.transform.SetSiblingIndex(0);
        
        CanvasGroup containerCanvasGroup = highlightContainer.AddComponent<CanvasGroup>();
        containerCanvasGroup.alpha = 0.7f;
        
        int maxCells = 16; // Assuming no item is larger than 4x4
        cellHighlights = new GameObject[maxCells];
        
        for (int i = 0; i < maxCells; i++)
        {
            GameObject highlight = new GameObject($"Highlight_{i}");
            highlight.transform.SetParent(highlightContainer.transform, false);
            
            Image highlightImage = highlight.AddComponent<Image>();
            highlightImage.color = validPlacementColor;
            
            RectTransform rect = highlight.GetComponent<RectTransform>();
            rect.sizeDelta = slotSize;
            
            highlight.SetActive(false);
            cellHighlights[i] = highlight;
        }
        
        highlightSystemInitialized = true;
    }
    
    /// <summary>
    /// Refresh the UI to show current container contents
    /// </summary>
    public void RefreshUI()
    {
        if (containerInventory == null) return;
        
        // Clear existing item UIs
        foreach (var itemUI in itemUIMapping.Values)
        {
            if (itemUI != null)
            {
                Destroy(itemUI.gameObject);
            }
        }
        itemUIMapping.Clear();
        
        // Create UI for each item
        foreach (InventoryItem item in containerInventory.GetAllItems())
        {
            CreateItemUI(item);
        }
    }
    
    /// <summary>
    /// Create UI representation for an item
    /// </summary>
    private void CreateItemUI(InventoryItem item)
    {
        if (item == null || itemUIPrefab == null || itemContainer == null) return;

        GameObject itemUIObj = Instantiate(itemUIPrefab, itemContainer);
        InventoryItemUI itemUI = itemUIObj.GetComponent<InventoryItemUI>();

        if (itemUI != null)
        {
            // Set up the item UI
            itemUI.Set(item.itemData, item.isRotated, slotSize);
            itemUI.UpdateItemTexts(item);

            // Position the item
            Vector2 itemPos = new Vector2(
                gridOffset.x + (item.position.x * (slotSize.x + GridSpacing.x)),
                gridOffset.y - (item.position.y * (slotSize.y + GridSpacing.y))
            );

            RectTransform rect = itemUIObj.GetComponent<RectTransform>();
            rect.anchoredPosition = itemPos;
            rect.pivot = new Vector2(0, 1);
            rect.anchorMin = new Vector2(0, 1);
            rect.anchorMax = new Vector2(0, 1);

            // Remove the regular InventoryItemUIDrag component if it exists
            InventoryItemUIDrag regularDrag = itemUIObj.GetComponent<InventoryItemUIDrag>();
            if (regularDrag != null)
            {
                Destroy(regularDrag);
            }

            // Add container-specific drag component
            ContainerInventoryItemUIDrag containerDrag = itemUIObj.GetComponent<ContainerInventoryItemUIDrag>();
            if (containerDrag == null)
            {
                containerDrag = itemUIObj.AddComponent<ContainerInventoryItemUIDrag>();
            }

            // Set up the drag component
            containerDrag.backendItem = item;

            // Store in mapping
            itemUIMapping[item.GetInstanceID()] = itemUI;
        }
    }
    
    public void OnPointerClick(PointerEventData eventData)
    {
        // Handle clicks on container background (deselect items)
        if (eventData.button != PointerEventData.InputButton.Left) return;
        
        if (eventData.pointerPress == gameObject || eventData.pointerPress == null)
        {
            var selectedItem = InventoryItemUIDrag.GetSelectedItem();
            if (selectedItem != null)
            {
                selectedItem.Deselect();
            }
        }
    }
}

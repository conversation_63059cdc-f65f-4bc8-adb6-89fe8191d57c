using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using static InventoryItem;

[RequireComponent(typeof(CanvasGroup))]
public class InventoryItemUIDrag : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nd<PERSON>rag<PERSON><PERSON><PERSON>, IPointerClickHandler
{
    private RectTransform rectTransform;
    private CanvasGroup canvasGroup;
    public InventoryUI inventoryUI; // Made public for container inventory access
    private Image itemImage;
    private Vector2 dragOffset;
    private Vector2Int adjustedGridPosition;

    // Container inventory references (for container items)
    public ContainerInventoryUI containerInventoryUI;
    public ContainerInventory containerInventory;

    // Drag state
    private bool isDragging;
    private bool temporaryIsRotated;
    private Vector2Int originalGridPosition;

    // Grid highlight objects
    private GameObject highlightContainer;
    private GameObject[] cellHighlights;

    [Header("UI Colors")]
    [Tooltip("Color for valid placement indicators")]
    [SerializeField] private Color validPlacementColor = new Color(0.5f, 1f, 0.5f, 0.5f);

    [Tooltip("Color for invalid placement indicators")]
    [SerializeField] private Color invalidPlacementColor = new Color(1f, 0.5f, 0.5f, 0.5f);

    [Tooltip("Color for item selection outline")]
    [SerializeField] private Color selectedOutlineColor = new Color(1f, 0.8f, 0.2f, 1f);

    [Tooltip("Alpha value for item during drag")]
    [Range(0.1f, 1f)]
    [SerializeField] private float dragAlpha = 0.6f;

    [Tooltip("Color tint for valid placement during drag")]
    [SerializeField] private Color validDragTint = new Color(1f, 1f, 1f, 0.6f);

    [Tooltip("Color tint for invalid placement during drag")]
    [SerializeField] private Color invalidDragTint = new Color(1f, 0.5f, 0.5f, 0.6f);

    [Header("Outline Settings")]
    [Tooltip("Width of the selection outline")]
    [Range(0.1f, 2f)]
    [SerializeField] private float outlineWidth = 1f;

    // Selection
    private BuiltInOutline outlineComponent;
    private static InventoryItemUIDrag currentlySelectedItem;

    // Public reference to the backend item
    public InventoryItem backendItem;

    [Header("Drop Settings")]
    [Tooltip("Reference to the player's transform for item dropping")]
    [SerializeField] private Transform playerTransform;
    [Tooltip("Offset from player position when dropping items")]
    [SerializeField] private Vector3 dropOffset = new Vector3(0, 0, 0);

    // Static variables and methods for external dragging operations (e.g., from hotbar)
    private static InventoryItem currentlyDraggedItem;
    private static InventoryItemUIDrag sharedDragInstance;
    private static bool currentDraggedItemRotated = false;

    private void Awake()
    {
        // Always ensure we have a RectTransform
        rectTransform = GetComponent<RectTransform>();
        if (rectTransform == null)
        {
            Debug.LogError("Critical error: RectTransform component missing on " + gameObject.name);
            // Try to add it if missing
            rectTransform = gameObject.AddComponent<RectTransform>();
        }

        canvasGroup = GetComponent<CanvasGroup>();
        inventoryUI = InventoryUI.Instance;
        itemImage = GetComponent<Image>();

        if (itemImage != null)
        {
            itemImage.raycastTarget = true;
            itemImage.preserveAspect = false;
            itemImage.type = Image.Type.Simple;
            itemImage.color = Color.white;
        }

        // Make sure the related InventoryItemUI has a rectTransform reference
        InventoryItemUI itemUI = GetComponent<InventoryItemUI>();
        if (itemUI != null && itemUI.rectTransform == null)
        {
            itemUI.rectTransform = rectTransform;
        }

        // Setup outline component
        SetupOutlineComponent();

        CreateGridHighlights();
    }

    private void SetupOutlineComponent()
    {
        outlineComponent = GetComponent<BuiltInOutline>();
        if (outlineComponent == null)
        {
            outlineComponent = gameObject.AddComponent<BuiltInOutline>();
        }

        outlineComponent.SetOutlineColor(selectedOutlineColor);
        outlineComponent.SetOutlineWidth(outlineWidth);
        outlineComponent.SetOutlineActive(false);
    }

    private void ShowOutline()
    {
        if (outlineComponent != null)
        {
            outlineComponent.SetOutlineActive(true);
        }
    }

    private void HideOutline()
    {
        if (outlineComponent != null)
        {
            outlineComponent.SetOutlineActive(false);
        }
    }

    private void CreateGridHighlights()
    {
        if (highlightContainer != null)
            Destroy(highlightContainer);

        highlightContainer = new GameObject("GridHighlights");
        Transform parent = inventoryUI.itemContainer;
        highlightContainer.transform.SetParent(parent, false);

        // Set the highlight container to be first (bottom-most) in the hierarchy
        highlightContainer.transform.SetSiblingIndex(0);

        CanvasGroup containerCanvasGroup = highlightContainer.AddComponent<CanvasGroup>();
        containerCanvasGroup.alpha = 0.7f;

        int maxCells = 16; // Assuming no item is larger than 4x4
        cellHighlights = new GameObject[maxCells];

        for (int i = 0; i < maxCells; i++)
        {
            GameObject highlight = new GameObject($"CellHighlight_{i}");
            highlight.transform.SetParent(highlightContainer.transform, false);

            Image highlightImage = highlight.AddComponent<Image>();
            highlightImage.color = validPlacementColor;
            highlightImage.raycastTarget = false;

            RectTransform highlightRect = highlight.GetComponent<RectTransform>();
            highlightRect.anchorMin = new Vector2(0, 1);
            highlightRect.anchorMax = new Vector2(0, 1);
            highlightRect.pivot = new Vector2(0, 1);

            cellHighlights[i] = highlight;
            highlight.SetActive(false);
        }

        highlightContainer.SetActive(false);
    }

    private void Start()
    {
        // If player transform is not set, try to find it
        if (playerTransform == null)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }
        }
    }

    private void Update()
    {
        if (isDragging && Input.GetMouseButtonDown(1))
            RotateItem();

        // Check for drop key when item is selected
        if (IsSelected() && Input.GetKeyDown(KeyCode.G))
        {
            DropItem();
        }
    }

    private void RotateItem()
    {
        if (!backendItem.itemData.canRotate) return;

        temporaryIsRotated = !temporaryIsRotated;

        Vector2Int gridPos = CalculateGridPosition();
        adjustedGridPosition = AdjustGridPositionForRotation(gridPos, temporaryIsRotated);

        InventoryItemUI itemUI = GetComponent<InventoryItemUI>();
        if (itemUI != null)
        {
            itemUI.SetRotation(temporaryIsRotated, inventoryUI.slotSize, isDragging);
        }

        UpdatePlacementValidity();
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        // Immediately return if not left mouse button
        if (eventData.button != PointerEventData.InputButton.Left)
        {
            eventData.pointerDrag = null;  // Prevent any drag operation
            return;
        }

        isDragging = true;
        temporaryIsRotated = backendItem.isRotated;
        originalGridPosition = backendItem.position;

        canvasGroup.alpha = dragAlpha;
        canvasGroup.blocksRaycasts = false;

        // Center the pivot for dragging
        rectTransform.pivot = new Vector2(0.5f, 0.5f);

        // Ensure the item is above the highlights in the hierarchy
        transform.SetAsLastSibling();

        // Ensure dragged item appears above both inventories
        EnsureDraggedItemOnTop();

        // Don't update rotation visually during drag - it will follow cursor
        InventoryItemUI itemUI = GetComponent<InventoryItemUI>();
        if (itemUI != null)
        {
            itemUI.SetDragMode(true);
        }

        highlightContainer.SetActive(true);
        // Ensure highlights stay below the item
        highlightContainer.transform.SetSiblingIndex(0);
        UpdatePlacementValidity();

        // Force selection of this item and deselect any other items
        if (currentlySelectedItem != null && currentlySelectedItem != this)
        {
            currentlySelectedItem.Deselect();
        }
        SelectItem(true); // Pass true to force selection even during drag
    }

    public void UpdatePlacementValidity()
    {
        Vector2Int gridPosition = CalculateGridPosition();
        adjustedGridPosition = AdjustGridPositionForRotation(gridPosition, temporaryIsRotated);

        Vector2Int size = temporaryIsRotated ?
            new Vector2Int(backendItem.itemData.size.y, backendItem.itemData.size.x) :
            backendItem.itemData.size;

        bool isValid = CanPlaceAt(adjustedGridPosition, size);

        itemImage.color = isValid ? validDragTint : invalidDragTint;

        UpdateGridHighlights(adjustedGridPosition, size, isValid);
    }

    private void UpdateGridHighlights(Vector2Int gridPos, Vector2Int size, bool isValid)
    {
        // Determine which inventory system to use
        Vector2 slotSize, spacing, gridOffset;
        int gridWidth, gridHeight;

        if (containerInventoryUI != null && containerInventory != null)
        {
            slotSize = containerInventoryUI.slotSize;
            spacing = containerInventoryUI.GridSpacing;
            gridOffset = containerInventoryUI.GridOffset;
            gridWidth = containerInventory.width;
            gridHeight = containerInventory.height;
        }
        else if (inventoryUI != null)
        {
            slotSize = inventoryUI.slotSize;
            spacing = inventoryUI.GridSpacing;
            gridOffset = inventoryUI.GridOffset;
            gridWidth = inventoryUI.inventoryManager.width;
            gridHeight = inventoryUI.inventoryManager.height;
        }
        else
        {
            Debug.LogError("No valid inventory UI reference found!");
            return;
        }

        Color highlightColor = isValid ? validPlacementColor : invalidPlacementColor;

        int cellIndex = 0;
        for (int y = 0; y < size.y; y++)
        {
            for (int x = 0; x < size.x; x++)
            {
                // Calculate the actual grid position for this cell
                int gridX = gridPos.x + x;
                int gridY = gridPos.y + y;

                // Skip cells outside the grid boundaries
                if (gridX < 0 || gridY < 0 || gridX >= gridWidth || gridY >= gridHeight)
                {
                    continue;
                }

                if (cellIndex < cellHighlights.Length)
                {
                    GameObject highlight = cellHighlights[cellIndex];
                    highlight.SetActive(true);

                    RectTransform highlightRect = highlight.GetComponent<RectTransform>();
                    highlightRect.sizeDelta = slotSize;

                    Vector2 cellPos = new Vector2(
                        gridOffset.x + gridX * (slotSize.x + spacing.x),
                        gridOffset.y - gridY * (slotSize.y + spacing.y)
                    );

                    highlightRect.anchorMin = new Vector2(0, 1);
                    highlightRect.anchorMax = new Vector2(0, 1);
                    highlightRect.pivot = new Vector2(0, 1);
                    highlightRect.anchoredPosition = cellPos;
                    highlight.GetComponent<Image>().color = highlightColor;

                    cellIndex++;
                }
            }
        }

        for (int i = cellIndex; i < cellHighlights.Length; i++)
            cellHighlights[i].SetActive(false);
    }

    private Vector2Int AdjustGridPositionForRotation(Vector2Int position, bool rotated)
    {
        if (!rotated) return position;

        Vector2Int adjustedPosition = position;
        Vector2Int size = new Vector2Int(backendItem.itemData.size.y, backendItem.itemData.size.x);

        // Determine which inventory system to use
        int gridWidth, gridHeight;
        if (containerInventoryUI != null && containerInventory != null)
        {
            gridWidth = containerInventory.width;
            gridHeight = containerInventory.height;
        }
        else if (inventoryUI != null)
        {
            gridWidth = inventoryUI.inventoryManager.width;
            gridHeight = inventoryUI.inventoryManager.height;
        }
        else
        {
            Debug.LogError("No valid inventory UI reference found!");
            return position;
        }

        if (position.x + size.x > gridWidth)
            adjustedPosition.x = gridWidth - size.x;

        if (position.y + size.y > gridHeight)
            adjustedPosition.y = gridHeight - size.y;

        adjustedPosition.x = Mathf.Max(0, adjustedPosition.x);
        adjustedPosition.y = Mathf.Max(0, adjustedPosition.y);

        return adjustedPosition;
    }

    private bool CanPlaceAt(Vector2Int pos, Vector2Int size)
    {
        // Early boundary check
        if (pos.x < 0 || pos.y < 0) return false;

        // Determine which inventory system to use
        int gridWidth, gridHeight;
        bool isValid;

        if (containerInventoryUI != null && containerInventory != null)
        {
            // Use container inventory
            gridWidth = containerInventory.width;
            gridHeight = containerInventory.height;

            if (pos.x + size.x > gridWidth || pos.y + size.y > gridHeight) return false;

            isValid = containerInventory.CanPlaceItem(backendItem, pos, size);
        }
        else if (inventoryUI != null)
        {
            // Use player inventory
            gridWidth = inventoryUI.inventoryManager.width;
            gridHeight = inventoryUI.inventoryManager.height;

            if (pos.x + size.x > gridWidth || pos.y + size.y > gridHeight) return false;

            isValid = inventoryUI.inventoryManager.CanPlaceItemWithRotation(
                backendItem,
                pos,
                temporaryIsRotated);
        }
        else
        {
            Debug.LogError("No valid inventory UI reference found!");
            return false;
        }

        // Special debug for the top-left corner to help identify the issue
        if (pos.x == 0 && pos.y == 0 && !isValid)
        {
            Debug.Log($"Top-left placement invalid: Item={backendItem.GetName()}, Size={size}, Rotated={temporaryIsRotated}");
        }

        return isValid;
    }

    public void OnDrag(PointerEventData eventData)
    {
        // Immediately return if not left mouse button or not already dragging
        if (eventData.button != PointerEventData.InputButton.Left || !isDragging)
        {
            eventData.pointerDrag = null;  // Prevent any drag operation
            return;
        }

        // Convert screen point to local point in parent's space
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            rectTransform.parent as RectTransform,
            eventData.position,
            eventData.pressEventCamera,
            out Vector2 localPoint
        );

        // Update position to follow cursor
        rectTransform.anchoredPosition = localPoint;

        UpdatePlacementValidity();
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        // Immediately return if not left mouse button or not already dragging
        if (eventData.button != PointerEventData.InputButton.Left || !isDragging)
        {
            eventData.pointerDrag = null;  // Prevent any drag operation
            return;
        }

        isDragging = false;
        canvasGroup.alpha = 1f;
        canvasGroup.blocksRaycasts = true;
        itemImage.color = Color.white;
        highlightContainer.SetActive(false);

        // Reset pivot to top-left
        rectTransform.pivot = new Vector2(0, 1);

        // Re-enable normal rotation handling
        InventoryItemUI itemUI = GetComponent<InventoryItemUI>();
        if (itemUI != null)
        {
            itemUI.SetDragMode(false);
        }

        // Check for cross-inventory transfer first
        if (TryTransferToOtherInventory(eventData))
        {
            return; // Transfer successful, exit early
        }

        Vector2Int gridSize = temporaryIsRotated ?
            new Vector2Int(backendItem.itemData.size.y, backendItem.itemData.size.x) :
            backendItem.itemData.size;

        // Double-check placement validity before confirming position
        if (!CanPlaceAt(adjustedGridPosition, gridSize))
        {
            Debug.LogWarning($"Invalid placement detected at {adjustedGridPosition}. Reverting to original position.");
            // Reset rotation state
            temporaryIsRotated = backendItem.isRotated;

            // Reset UI position and rotation
            if (itemUI != null)
            {
                Vector2 slotSize = containerInventoryUI != null ? containerInventoryUI.slotSize : inventoryUI.slotSize;
                itemUI.SetRotation(backendItem.isRotated, slotSize);
            }

            // Force position update to original
            UpdateItemPosition(originalGridPosition);

            // Ensure the backend state matches
            inventoryUI.inventoryManager.UpdateItemPosition(
                backendItem,
                originalGridPosition,
                backendItem.GetRotatedSize(),
                backendItem.isRotated
            );

            return;
        }

        // Update position in the appropriate inventory system
        bool success = false;
        if (containerInventoryUI != null && containerInventory != null)
        {
            // Update in container inventory
            success = containerInventory.UpdateItemPosition(
                backendItem,
                adjustedGridPosition,
                gridSize,
                temporaryIsRotated
            );
        }
        else if (inventoryUI != null)
        {
            // Update in player inventory
            success = inventoryUI.inventoryManager.UpdateItemPosition(
                backendItem,
                adjustedGridPosition,
                gridSize,
                temporaryIsRotated
            );
        }

        if (!success)
        {
            Debug.LogWarning($"Backend rejected position update. Reverting to original position.");
            temporaryIsRotated = backendItem.isRotated;

            // Reset UI position and rotation
            if (itemUI != null)
            {
                Vector2 slotSize = containerInventoryUI != null ? containerInventoryUI.slotSize : inventoryUI.slotSize;
                itemUI.SetRotation(backendItem.isRotated, slotSize);
            }

            // Force position update to original
            UpdateItemPosition(originalGridPosition);

            // Ensure the backend state matches
            if (containerInventoryUI != null && containerInventory != null)
            {
                containerInventory.UpdateItemPosition(
                    backendItem,
                    originalGridPosition,
                    backendItem.GetRotatedSize(),
                    backendItem.isRotated
                );
            }
            else if (inventoryUI != null)
            {
                inventoryUI.inventoryManager.UpdateItemPosition(
                    backendItem,
                    originalGridPosition,
                    backendItem.GetRotatedSize(),
                    backendItem.isRotated
                );
            }
        }
        else
        {
            // Refresh the appropriate UI
            if (containerInventoryUI != null)
            {
                containerInventoryUI.RefreshInventoryUI();
            }
            else if (inventoryUI != null)
            {
                inventoryUI.RefreshInventoryUI();
            }
        }
    }

    /// <summary>
    /// Try to transfer item to another inventory (container or player)
    /// </summary>
    private bool TryTransferToOtherInventory(PointerEventData eventData)
    {
        // Find dual inventory UI
        DualInventoryUI dualInventoryUI = FindObjectOfType<DualInventoryUI>();
        if (dualInventoryUI == null || !dualInventoryUI.IsOpen())
        {
            return false; // No dual inventory open
        }

        // Check what UI element we're over
        GameObject targetObject = eventData.pointerEnter;
        if (targetObject == null) return false;

        // Check if we're dropping on a container inventory UI
        ContainerInventoryUI containerUI = targetObject.GetComponentInParent<ContainerInventoryUI>();
        if (containerUI != null)
        {
            // Transfer from player to container
            if (dualInventoryUI.TransferToContainer(backendItem))
            {
                Debug.Log($"Transferred {backendItem.GetName()} to container");
                return true;
            }
        }

        // Check if we're dropping on player inventory UI
        InventoryUI playerInventoryUI = targetObject.GetComponentInParent<InventoryUI>();
        if (playerInventoryUI != null && playerInventoryUI != inventoryUI)
        {
            // This might be a transfer from container to player
            if (dualInventoryUI.TransferToPlayer(backendItem))
            {
                Debug.Log($"Transferred {backendItem.GetName()} to player inventory");
                return true;
            }
        }

        return false;
    }

    private void UpdateItemPosition(Vector2Int gridPos = default)
    {
        // Determine which inventory system to use
        Vector2 gridOffset;
        Vector2 slotSize;
        Vector2 gridSpacing;

        if (containerInventoryUI != null && containerInventory != null)
        {
            // Use container inventory
            gridOffset = containerInventoryUI.GridOffset;
            slotSize = containerInventoryUI.slotSize;
            gridSpacing = containerInventoryUI.GridSpacing;
        }
        else if (inventoryUI != null)
        {
            // Use player inventory
            gridOffset = inventoryUI.GridOffset;
            slotSize = inventoryUI.slotSize;
            gridSpacing = inventoryUI.GridSpacing;
        }
        else
        {
            Debug.LogError("No valid inventory UI reference found!");
            return;
        }

        Vector2 uiPosition = new Vector2(
            gridOffset.x + (gridPos == default ? adjustedGridPosition.x : gridPos.x) * (slotSize.x + gridSpacing.x),
            gridOffset.y - (gridPos == default ? adjustedGridPosition.y : gridPos.y) * (slotSize.y + gridSpacing.y)
        );

        // Use lerping when not dragging
        InventoryItemUI itemUI = GetComponent<InventoryItemUI>();
        if (itemUI != null)
        {
            itemUI.SetTargetPosition(uiPosition);
        }
    }

    private Vector2Int CalculateGridPosition()
    {
        // Determine which inventory system to use
        Vector2 gridOffset;
        Vector2 slotSize;
        Vector2 gridSpacing;
        int gridWidth, gridHeight;

        if (containerInventoryUI != null && containerInventory != null)
        {
            // Use container inventory
            gridOffset = containerInventoryUI.GridOffset;
            slotSize = containerInventoryUI.slotSize;
            gridSpacing = containerInventoryUI.GridSpacing;
            gridWidth = containerInventory.width;
            gridHeight = containerInventory.height;
        }
        else if (inventoryUI != null)
        {
            // Use player inventory
            gridOffset = inventoryUI.GridOffset;
            slotSize = inventoryUI.slotSize;
            gridSpacing = inventoryUI.GridSpacing;
            gridWidth = inventoryUI.inventoryManager.width;
            gridHeight = inventoryUI.inventoryManager.height;
        }
        else
        {
            Debug.LogError("No valid inventory UI reference found!");
            return Vector2Int.zero;
        }

        Vector2 itemLocalPos = rectTransform.anchoredPosition - gridOffset;
        float cellWidth = slotSize.x + gridSpacing.x;
        float cellHeight = slotSize.y + gridSpacing.y;

        // Get the size of the item
        Vector2 itemSize = rectTransform.sizeDelta;

        // Calculate position based on current rotation state
        if (temporaryIsRotated)
        {
            itemSize = new Vector2(itemSize.y, itemSize.x);
        }

        // Adjust position to account for centered pivot during drag
        if (isDragging)
        {
            // Increase both X and Y adjustments to center the placement box better
            itemLocalPos.x -= (itemSize.x * 0.5f) - (cellWidth * 0.35f);  // Further increased rightward adjustment
            itemLocalPos.y += (itemSize.y * 0.5f) - (cellHeight * 0.15f); // Added downward adjustment
        }

        int gridX = Mathf.FloorToInt(itemLocalPos.x / cellWidth);
        int gridY = Mathf.FloorToInt(-itemLocalPos.y / cellHeight);

        // Clamp to valid grid range
        gridX = Mathf.Clamp(gridX, 0, gridWidth - 1);
        gridY = Mathf.Clamp(gridY, 0, gridHeight - 1);

        return new Vector2Int(gridX, gridY);
    }

    private void OnDestroy()
    {
        if (highlightContainer != null)
            Destroy(highlightContainer);

        // Clean up selection if this item was selected
        if (currentlySelectedItem == this)
            currentlySelectedItem = null;
    }

    // Selection Methods

    public void OnPointerClick(PointerEventData eventData)
    {
        try
        {
            // Only handle left mouse button clicks
            if (eventData.button == PointerEventData.InputButton.Left)
            {
                Debug.Log($"Clicked on item: {(backendItem != null ? backendItem.GetName() : "unknown")}");
                SelectItem();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in OnPointerClick: {e.Message}\n{e.StackTrace}");
        }
    }

    public void SelectItem(bool forceDuringDrag = false)
    {
        try
        {
            Debug.Log($"Selecting item: {backendItem.GetName()}");

            // Don't do selection if we're dragging or animating, unless forced
            InventoryItemUI itemUI = GetComponent<InventoryItemUI>();
            if (!forceDuringDrag && itemUI != null && (itemUI.IsDragging || itemUI.IsAnimating))
            {
                return;
            }

            // Clear previous selection if it exists and isn't this item
            if (currentlySelectedItem != null && currentlySelectedItem != this)
            {
                currentlySelectedItem.Deselect();
            }

            // Set this as the currently selected item
            currentlySelectedItem = this;

            // Show the outline
            ShowOutline();

            // Get the ItemUI component to reset visuals if needed
            if (itemUI != null)
            {
                // Only reset visuals if we're not in the middle of an operation
                if (!itemUI.IsDragging && !itemUI.IsAnimating)
                {
                    itemUI.ResetItemVisuals();
                }
                
                // Ensure text components are always on top of the outline
                itemUI.EnsureTextComponentsOnTop();
                
                // Use a coroutine to ensure text components are positioned correctly after outline is shown
                StartCoroutine(EnsureTextOnTopAfterFrame(itemUI));
            }

            Debug.Log($"Item selected successfully: {backendItem.GetName()}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in SelectItem: {e.Message}\n{e.StackTrace}");
        }
    }
    
    private System.Collections.IEnumerator EnsureTextOnTopAfterFrame(InventoryItemUI itemUI)
    {
        // Wait for the end of frame to ensure outline is fully rendered
        yield return new WaitForEndOfFrame();
        
        // Ensure text components are on top
        if (itemUI != null)
        {
            itemUI.EnsureTextComponentsOnTop();
        }
    }

    public void Deselect()
    {
        try
        {
            Debug.Log($"Deselecting item: {(backendItem != null ? backendItem.GetName() : "unknown")}");

            // Don't deselect if we're dragging or animating
            InventoryItemUI itemUI = GetComponent<InventoryItemUI>();
            if (itemUI != null && (itemUI.IsDragging || itemUI.IsAnimating))
            {
                return;
            }

            // Hide the outline
            HideOutline();

            // Ensure text components remain on top even after outline is hidden
            if (itemUI != null)
            {
                itemUI.EnsureTextComponentsOnTop();
            }

            // Clear the static reference if it points to this instance
            if (currentlySelectedItem == this)
            {
                currentlySelectedItem = null;
                Debug.Log("Cleared static selected item reference");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in Deselect: {e.Message}");
        }
    }

    public bool IsSelected()
    {
        return currentlySelectedItem == this;
    }

    // Static method to get currently selected item
    public static InventoryItemUIDrag GetSelectedItem()
    {
        try
        {
            // Validate that the currentlySelectedItem is still valid
            if (currentlySelectedItem != null)
            {
                // Check if the object is destroyed or being destroyed
                if (currentlySelectedItem == null || currentlySelectedItem.gameObject == null)
                {
                    Debug.LogWarning("Currently selected item reference is invalid (destroyed) - clearing reference");
                    currentlySelectedItem = null;
                    return null;
                }

                // Verify that the instance has necessary components
                if (currentlySelectedItem.backendItem == null)
                {
                    Debug.LogWarning("Currently selected item has null backendItem reference - clearing selection");
                    currentlySelectedItem = null;
                    return null;
                }
            }

            return currentlySelectedItem;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in GetSelectedItem: {e.Message}");
            // Reset static reference in case of error
            currentlySelectedItem = null;
            return null;
        }
    }

    private void DropItem()
    {
        if (backendItem == null || playerTransform == null) return;

        // Drop the item through the appropriate inventory manager with player position for animation
        if (containerInventoryUI != null && containerInventory != null)
        {
            // Remove from container and drop in world
            containerInventory.RemoveItem(backendItem);
            // Note: Container items don't have a drop method, so we'll need to handle this differently
            // For now, just remove from container - the item will need to be spawned in world manually
            Debug.LogWarning("Container item drop not fully implemented - item removed from container");
        }
        else if (inventoryUI != null)
        {
            // Drop from player inventory
            inventoryUI.inventoryManager.DropItem(backendItem, Vector3.zero, playerTransform.position);
        }

        // Deselect the item
        Deselect();

        // Destroy the UI representation
        Destroy(gameObject);
    }

    /// <summary>
    /// Set up currently dragged item - can be called from hotbar
    /// </summary>
    public static void SetCurrentlyDraggedItem(InventoryItem item)
    {
        currentlyDraggedItem = item;
        currentDraggedItemRotated = item != null ? item.isRotated : false;

        // Find a suitable instance to use its highlight resources if needed
        if (sharedDragInstance == null)
        {
            sharedDragInstance = FindObjectOfType<InventoryItemUIDrag>();
        }

        // Show highlights if we have a valid instance
        if (sharedDragInstance != null && item != null)
        {
            sharedDragInstance.highlightContainer.SetActive(true);
        }
    }

    /// <summary>
    /// Set rotation state of currently dragged item
    /// </summary>
    public static void SetCurrentDraggedItemRotation(bool rotated)
    {
        if (currentlyDraggedItem == null || sharedDragInstance == null) return;

        currentDraggedItemRotated = rotated;

        // Update highlights with new rotation state
        Vector2 mousePosition = Input.mousePosition;
        UpdateCurrentDragHighlights(mousePosition);
    }

    /// <summary>
    /// Update highlights for current dragged item - called during hotbar dragging
    /// </summary>
    public static void UpdateCurrentDragHighlights(Vector2 position)
    {
        // Skip if invalid state
        if (currentlyDraggedItem == null || sharedDragInstance == null) return;

        // Check if mouse is over the inventory grid
        if (sharedDragInstance.inventoryUI == null ||
            !IsPointerOverInventoryGrid(position, sharedDragInstance.inventoryUI.GridRectTransform))
        {
            // Hide highlights if not over grid
            if (sharedDragInstance != null)
                sharedDragInstance.HideAllGridHighlights();
            return;
        }

        // Calculate grid position from screen point
        Vector2Int gridPos = sharedDragInstance.CalculateGridPositionFromScreenPoint(position);

        // Get correct size based on rotation state
        Vector2Int size = currentDraggedItemRotated ?
            new Vector2Int(currentlyDraggedItem.itemData.size.y, currentlyDraggedItem.itemData.size.x) :
            currentlyDraggedItem.itemData.size;

        // Check if placement is valid
        bool canPlace = false;

        if (sharedDragInstance.inventoryUI != null && sharedDragInstance.inventoryUI.inventoryManager != null)
        {
            // Use manager for accurate validation
            canPlace = sharedDragInstance.inventoryUI.inventoryManager.CanPlaceItemWithRotation(
                currentlyDraggedItem,
                gridPos,
                currentDraggedItemRotated
            );
        }
        else if (sharedDragInstance != null)
        {
            // Fallback to local validation
            canPlace = sharedDragInstance.CanPlaceAt(gridPos, size);
        }

        // Update highlight visuals
        if (sharedDragInstance != null)
        {
            sharedDragInstance.UpdateGridHighlights(gridPos, size, canPlace);
        }
    }

    /// <summary>
    /// Clear current dragged item state - call at end of drag operation
    /// </summary>
    public static void ClearCurrentDraggedItem()
    {
        currentlyDraggedItem = null;
        currentDraggedItemRotated = false;

        // Clear highlights from shared instance
        if (sharedDragInstance != null && sharedDragInstance.highlightContainer != null)
        {
            sharedDragInstance.highlightContainer.SetActive(false);
        }

        // Also try to find and clear highlights from any other instances
        InventoryItemUIDrag[] allInstances = FindObjectsOfType<InventoryItemUIDrag>();
        foreach (var instance in allInstances)
        {
            if (instance.highlightContainer != null)
            {
                instance.highlightContainer.SetActive(false);
            }
        }
    }

    // Calculate grid position from screen point - used for hotbar dragging
    private Vector2Int CalculateGridPositionFromScreenPoint(Vector2 screenPoint)
    {
        if (inventoryUI == null) return Vector2Int.zero;

        // Convert screen point to local point in the inventory grid
        RectTransform gridTransform = inventoryUI.GridRectTransform;
        Vector2 localPoint;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            gridTransform,
            screenPoint,
            null,
            out localPoint
        );

        // Adjust for grid offset
        Vector2 offsetPoint = localPoint - inventoryUI.GridOffset;

        // Calculate grid position
        float cellWidth = inventoryUI.slotSize.x + inventoryUI.GridSpacing.x;
        float cellHeight = inventoryUI.slotSize.y + inventoryUI.GridSpacing.y;

        int gridX = Mathf.FloorToInt(offsetPoint.x / cellWidth);
        int gridY = Mathf.FloorToInt(-offsetPoint.y / cellHeight);

        // Clamp to inventory bounds
        gridX = Mathf.Clamp(gridX, 0, inventoryUI.inventoryManager.width - 1);
        gridY = Mathf.Clamp(gridY, 0, inventoryUI.inventoryManager.height - 1);

        return new Vector2Int(gridX, gridY);
    }

    // Helper to check if pointer is over the inventory grid
    private static bool IsPointerOverInventoryGrid(Vector2 screenPosition, RectTransform gridRectTransform)
    {
        if (gridRectTransform == null) return false;
        return RectTransformUtility.RectangleContainsScreenPoint(gridRectTransform, screenPosition, null);
    }

    // Helper to hide all grid highlights
    private void HideAllGridHighlights()
    {
        if (cellHighlights == null) return;
        foreach (var highlight in cellHighlights)
        {
            if (highlight != null) highlight.SetActive(false);
        }
        if (highlightContainer != null) highlightContainer.SetActive(false);
    }

    /// <summary>
    /// Ensure dragged item appears above both player and container inventories
    /// </summary>
    private void EnsureDraggedItemOnTop()
    {
        // Find the highest canvas sorting order among all inventory UIs
        Canvas[] allCanvases = FindObjectsOfType<Canvas>();
        int highestSortOrder = 0;

        foreach (Canvas canvas in allCanvases)
        {
            // Check if this canvas belongs to an inventory UI
            if (canvas.GetComponentInChildren<InventoryUI>() != null ||
                canvas.GetComponentInChildren<ContainerInventoryUI>() != null)
            {
                if (canvas.sortingOrder > highestSortOrder)
                {
                    highestSortOrder = canvas.sortingOrder;
                }
            }
        }

        // Set this item's canvas to be above all inventory canvases
        Canvas itemCanvas = GetComponentInParent<Canvas>();
        if (itemCanvas != null)
        {
            itemCanvas.sortingOrder = highestSortOrder + 10;
        }
    }
}
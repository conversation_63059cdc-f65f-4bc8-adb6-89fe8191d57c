{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21312, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21312, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21312, "tid": 9640, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21312, "tid": 9640, "ts": 1753305213633691, "dur": 471, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21312, "tid": 9640, "ts": 1753305213636662, "dur": 634, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21312, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21312, "tid": 1, "ts": 1753305211384866, "dur": 4719, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753305211389590, "dur": 35997, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753305211425596, "dur": 60488, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21312, "tid": 9640, "ts": 1753305213637300, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 21312, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211380832, "dur": 66, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211380899, "dur": 2245999, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211381521, "dur": 3274, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211384802, "dur": 1237, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386043, "dur": 262, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386309, "dur": 10, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386320, "dur": 47, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386369, "dur": 1, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386371, "dur": 84, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386458, "dur": 106, "ph": "X", "name": "ReadAsync 23", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386567, "dur": 111, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386681, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386683, "dur": 75, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386765, "dur": 8, "ph": "X", "name": "ProcessMessages 2286", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386776, "dur": 98, "ph": "X", "name": "ReadAsync 2286", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386878, "dur": 2, "ph": "X", "name": "ProcessMessages 1405", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386881, "dur": 75, "ph": "X", "name": "ReadAsync 1405", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386957, "dur": 2, "ph": "X", "name": "ProcessMessages 2554", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386960, "dur": 35, "ph": "X", "name": "ReadAsync 2554", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386996, "dur": 1, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211386997, "dur": 29, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387031, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387033, "dur": 39, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387075, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387078, "dur": 41, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387121, "dur": 1, "ph": "X", "name": "ProcessMessages 1297", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387123, "dur": 117, "ph": "X", "name": "ReadAsync 1297", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387243, "dur": 105, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387349, "dur": 1, "ph": "X", "name": "ProcessMessages 1977", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387352, "dur": 101, "ph": "X", "name": "ReadAsync 1977", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387454, "dur": 1, "ph": "X", "name": "ProcessMessages 2417", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387457, "dur": 111, "ph": "X", "name": "ReadAsync 2417", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387569, "dur": 1, "ph": "X", "name": "ProcessMessages 1818", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387571, "dur": 37, "ph": "X", "name": "ReadAsync 1818", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387610, "dur": 1, "ph": "X", "name": "ProcessMessages 2426", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387612, "dur": 75, "ph": "X", "name": "ReadAsync 2426", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387688, "dur": 1, "ph": "X", "name": "ProcessMessages 1421", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387690, "dur": 21, "ph": "X", "name": "ReadAsync 1421", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387713, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387715, "dur": 34, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387752, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387775, "dur": 20, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387799, "dur": 20, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387822, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387849, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387874, "dur": 23, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387899, "dur": 18, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387920, "dur": 38, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387961, "dur": 22, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211387985, "dur": 27, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388015, "dur": 159, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388176, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388200, "dur": 23, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388227, "dur": 21, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388252, "dur": 18, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388272, "dur": 29, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388304, "dur": 23, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388329, "dur": 21, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388353, "dur": 18, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388373, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388395, "dur": 18, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388416, "dur": 20, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388438, "dur": 18, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388459, "dur": 98, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388559, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388582, "dur": 23, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388607, "dur": 21, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388631, "dur": 17, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388650, "dur": 19, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388671, "dur": 22, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388696, "dur": 19, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388718, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388739, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388759, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388782, "dur": 23, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388808, "dur": 20, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388831, "dur": 154, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211388987, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389012, "dur": 19, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389034, "dur": 29, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389068, "dur": 19, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389089, "dur": 77, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389168, "dur": 1, "ph": "X", "name": "ProcessMessages 1863", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389169, "dur": 20, "ph": "X", "name": "ReadAsync 1863", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389192, "dur": 17, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389211, "dur": 20, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389234, "dur": 22, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389258, "dur": 19, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389279, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389301, "dur": 80, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389382, "dur": 2, "ph": "X", "name": "ProcessMessages 2315", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389385, "dur": 21, "ph": "X", "name": "ReadAsync 2315", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389409, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389436, "dur": 19, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389458, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389478, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389500, "dur": 23, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389524, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389526, "dur": 20, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389548, "dur": 17, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389568, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389588, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389610, "dur": 19, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389632, "dur": 19, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389654, "dur": 18, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389674, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389701, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389724, "dur": 21, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389747, "dur": 20, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389770, "dur": 43, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389815, "dur": 19, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389837, "dur": 19, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389859, "dur": 19, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389881, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389903, "dur": 21, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389927, "dur": 20, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389950, "dur": 19, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389971, "dur": 23, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211389997, "dur": 18, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390018, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390040, "dur": 16, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390058, "dur": 22, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390083, "dur": 22, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390108, "dur": 20, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390131, "dur": 17, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390150, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390175, "dur": 36, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390214, "dur": 18, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390234, "dur": 18, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390255, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390280, "dur": 20, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390303, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390324, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390326, "dur": 17, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390346, "dur": 20, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390368, "dur": 20, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390391, "dur": 21, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390414, "dur": 17, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390434, "dur": 19, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390456, "dur": 21, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390478, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390481, "dur": 33, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390516, "dur": 17, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390537, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390559, "dur": 28, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390590, "dur": 19, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390612, "dur": 18, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390633, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390655, "dur": 20, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390677, "dur": 17, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390697, "dur": 17, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390717, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390744, "dur": 23, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390770, "dur": 19, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390791, "dur": 25, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390819, "dur": 19, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390841, "dur": 27, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390872, "dur": 20, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390894, "dur": 20, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390917, "dur": 21, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390941, "dur": 18, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390962, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211390984, "dur": 23, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391008, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391009, "dur": 22, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391034, "dur": 17, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391074, "dur": 28, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391103, "dur": 1, "ph": "X", "name": "ProcessMessages 1561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391105, "dur": 19, "ph": "X", "name": "ReadAsync 1561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391127, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391147, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391176, "dur": 16, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391194, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391218, "dur": 19, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391240, "dur": 26, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391268, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391271, "dur": 26, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391299, "dur": 1, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391300, "dur": 22, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391325, "dur": 18, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391345, "dur": 21, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391369, "dur": 19, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391390, "dur": 19, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391412, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391433, "dur": 19, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391454, "dur": 18, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391475, "dur": 21, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391498, "dur": 18, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391519, "dur": 20, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391541, "dur": 20, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391563, "dur": 20, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391586, "dur": 18, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391607, "dur": 19, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391628, "dur": 17, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391648, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391670, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391689, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391714, "dur": 20, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391737, "dur": 20, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391759, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391783, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391784, "dur": 74, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391861, "dur": 37, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391900, "dur": 1, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391902, "dur": 31, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391937, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391940, "dur": 45, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391989, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211391992, "dur": 38, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392032, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392033, "dur": 28, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392064, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392066, "dur": 48, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392115, "dur": 1, "ph": "X", "name": "ProcessMessages 1175", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392117, "dur": 28, "ph": "X", "name": "ReadAsync 1175", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392148, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392150, "dur": 38, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392190, "dur": 1, "ph": "X", "name": "ProcessMessages 1379", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392192, "dur": 34, "ph": "X", "name": "ReadAsync 1379", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392229, "dur": 33, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392265, "dur": 2, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392268, "dur": 39, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392310, "dur": 29, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392342, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392344, "dur": 34, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392379, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392381, "dur": 27, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392411, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392413, "dur": 37, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392451, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392453, "dur": 27, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392483, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392486, "dur": 36, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392524, "dur": 1, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392525, "dur": 28, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392555, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392558, "dur": 34, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392593, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392594, "dur": 27, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392624, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392626, "dur": 25, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392653, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392655, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392683, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392685, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392710, "dur": 28, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392742, "dur": 4, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392747, "dur": 24, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392773, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392828, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392866, "dur": 2, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392869, "dur": 24, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392895, "dur": 30, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392929, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392985, "dur": 1, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211392986, "dur": 50, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393039, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393076, "dur": 1, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393078, "dur": 104, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393184, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393228, "dur": 1, "ph": "X", "name": "ProcessMessages 1881", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393230, "dur": 65, "ph": "X", "name": "ReadAsync 1881", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393297, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393324, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393326, "dur": 77, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393406, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393432, "dur": 26, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393462, "dur": 18, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393482, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393526, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393549, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393573, "dur": 49, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393624, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393663, "dur": 17, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393683, "dur": 38, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393723, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393746, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393770, "dur": 46, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393819, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393843, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393868, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393870, "dur": 42, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393914, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393941, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393963, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211393965, "dur": 37, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394004, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394030, "dur": 20, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394053, "dur": 40, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394096, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394118, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394141, "dur": 43, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394187, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394207, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394229, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394249, "dur": 9, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394259, "dur": 35, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394297, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394320, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394342, "dur": 44, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394388, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394409, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394431, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394452, "dur": 41, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394495, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394594, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394596, "dur": 39, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394637, "dur": 1, "ph": "X", "name": "ProcessMessages 1255", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394638, "dur": 20, "ph": "X", "name": "ReadAsync 1255", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394662, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394663, "dur": 112, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394777, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394805, "dur": 1, "ph": "X", "name": "ProcessMessages 1238", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394806, "dur": 18, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394827, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394851, "dur": 66, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394920, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394941, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211394963, "dur": 49, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395015, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395037, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395056, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395077, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395123, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395147, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395170, "dur": 44, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395216, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395245, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395267, "dur": 47, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395317, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395339, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395341, "dur": 23, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395366, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395367, "dur": 18, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395387, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395427, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395448, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395471, "dur": 19, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395493, "dur": 19, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395514, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395534, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395558, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395609, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395641, "dur": 17, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395660, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395700, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395724, "dur": 20, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395746, "dur": 16, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395764, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395799, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395821, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395842, "dur": 44, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395888, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395934, "dur": 1, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395935, "dur": 35, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395972, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211395993, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396015, "dur": 17, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396035, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396075, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396096, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396118, "dur": 45, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396166, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396188, "dur": 21, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396211, "dur": 45, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396258, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396284, "dur": 106, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396404, "dur": 6, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396413, "dur": 192, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396607, "dur": 2, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396610, "dur": 38, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396652, "dur": 1, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396654, "dur": 28, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396684, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396685, "dur": 43, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396731, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396754, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396776, "dur": 45, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396823, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396849, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396871, "dur": 41, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396914, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396937, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396959, "dur": 19, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211396980, "dur": 19, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397002, "dur": 42, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397047, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397049, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397071, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397108, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397130, "dur": 19, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397152, "dur": 22, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397177, "dur": 23, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397203, "dur": 23, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397229, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397249, "dur": 30, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397281, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397305, "dur": 19, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397328, "dur": 44, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397375, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397398, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397422, "dur": 38, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397462, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397484, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397505, "dur": 17, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397524, "dur": 34, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397560, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397585, "dur": 19, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397607, "dur": 40, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397649, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397673, "dur": 20, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397696, "dur": 38, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397736, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397766, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397767, "dur": 24, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397793, "dur": 17, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397812, "dur": 32, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397847, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397869, "dur": 20, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397892, "dur": 17, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397911, "dur": 31, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397944, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397965, "dur": 20, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211397987, "dur": 17, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398007, "dur": 47, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398056, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398091, "dur": 17, "ph": "X", "name": "ReadAsync 1093", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398111, "dur": 35, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398148, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398171, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398173, "dur": 21, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398197, "dur": 36, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398236, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398267, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398269, "dur": 18, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398290, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398318, "dur": 25, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398345, "dur": 19, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398366, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398408, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398436, "dur": 18, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398458, "dur": 38, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398499, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398521, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398543, "dur": 48, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398594, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398624, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398626, "dur": 125, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398755, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398757, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398801, "dur": 1, "ph": "X", "name": "ProcessMessages 1640", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398803, "dur": 34, "ph": "X", "name": "ReadAsync 1640", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398839, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398862, "dur": 22, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398887, "dur": 31, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398921, "dur": 15, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398939, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211398988, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399011, "dur": 22, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399036, "dur": 20, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399060, "dur": 20, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399082, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399104, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399146, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399166, "dur": 129, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399301, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399387, "dur": 339, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399729, "dur": 211, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399946, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399972, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211399993, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400011, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400030, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400049, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400075, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400146, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400150, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400211, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400213, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400260, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400262, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400321, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400324, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400398, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400402, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400444, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400447, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400497, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400500, "dur": 51, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400555, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400558, "dur": 37, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400598, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400600, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400648, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400650, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400697, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400699, "dur": 103, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400807, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400857, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400860, "dur": 41, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400905, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400907, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400946, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400949, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400986, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211400988, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401023, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401025, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401064, "dur": 11, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401077, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401126, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401129, "dur": 44, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401176, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401178, "dur": 36, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401218, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401220, "dur": 36, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401260, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401262, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401302, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401305, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401354, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401357, "dur": 42, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401402, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401405, "dur": 41, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401450, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401452, "dur": 48, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401504, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401507, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401548, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401550, "dur": 32, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401585, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401587, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401630, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401633, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401673, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401675, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401716, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401719, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401766, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401769, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401797, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401799, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401831, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401834, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401857, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401858, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401907, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401909, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401943, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401945, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401976, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211401980, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402014, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402016, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402061, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402064, "dur": 54, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402121, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402124, "dur": 40, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402166, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402169, "dur": 57, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402229, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402231, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402267, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402270, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402305, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402308, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402349, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402352, "dur": 41, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402396, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402399, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402432, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402435, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402470, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402473, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402511, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402514, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402553, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402555, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402601, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211402603, "dur": 14397, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417009, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417015, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417052, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417054, "dur": 56, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417124, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417130, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417160, "dur": 646, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417812, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417875, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211417878, "dur": 8134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426019, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426058, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426060, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426082, "dur": 285, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426378, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426502, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426504, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426617, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426628, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426665, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426667, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426723, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426765, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426768, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426807, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426809, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426842, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426873, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426903, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211426941, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427051, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427082, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427109, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427201, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427242, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427244, "dur": 457, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427708, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427744, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427746, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427788, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427817, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427849, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427859, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427926, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211427966, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428015, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428017, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428050, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428081, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428121, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428123, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428153, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428182, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428210, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428272, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428301, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428351, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428352, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428383, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428385, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428437, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428439, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428478, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428544, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428546, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428593, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428621, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428623, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428654, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428656, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428686, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428730, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428793, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428848, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211428967, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429006, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429007, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429088, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429160, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429162, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429206, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429233, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429235, "dur": 69, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429308, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429343, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429389, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429526, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429528, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429555, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429614, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429616, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429667, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429747, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429790, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429792, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429832, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429879, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429921, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211429954, "dur": 50, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430007, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430010, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430055, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430057, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430094, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430130, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430214, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430216, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430285, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430351, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430353, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430508, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430510, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430532, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430534, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211430575, "dur": 724, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431304, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431354, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431357, "dur": 40, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431402, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431478, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431543, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431545, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431626, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431629, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431706, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431709, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431860, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431942, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431944, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211431998, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432153, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432218, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432244, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432283, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432339, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432404, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432515, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432546, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432591, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432618, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432685, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432753, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211432785, "dur": 4425, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211437224, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211437228, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211437432, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211437436, "dur": 42748, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211480193, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211480197, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211480230, "dur": 2846, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211483082, "dur": 5223, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488313, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488316, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488364, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488366, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488433, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488477, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488478, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488510, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488512, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488559, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488593, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211488622, "dur": 810, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211489435, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211489437, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211489468, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211489470, "dur": 283, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211489757, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211489789, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490210, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490212, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490256, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490258, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490293, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490297, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490475, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490505, "dur": 354, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490862, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490896, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490898, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490934, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490970, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211490998, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211491025, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211491130, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211491157, "dur": 477, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211491637, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211491664, "dur": 721, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492391, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492415, "dur": 361, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492780, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492815, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492817, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492894, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492926, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211492928, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493049, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493050, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493083, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493085, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493181, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493182, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493229, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493231, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493274, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493276, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493312, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493314, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493438, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211493465, "dur": 632, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494101, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494103, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494136, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494138, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494159, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494314, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494316, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494360, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211494362, "dur": 970, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495337, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495360, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495404, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495437, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495439, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495775, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495815, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495817, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211495835, "dur": 392, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496232, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496264, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496294, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496334, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496336, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496353, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496387, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496389, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496426, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496428, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496464, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496466, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496489, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496518, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496520, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496544, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496545, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496583, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496606, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496608, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496643, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496646, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496687, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496689, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496731, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496764, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496765, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496795, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496798, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496831, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496833, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496877, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496879, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496924, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496926, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496953, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496954, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496986, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211496988, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497023, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497025, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497051, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497053, "dur": 27, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497083, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497085, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497107, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497109, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497128, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497197, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497200, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497237, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497240, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497274, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497278, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497313, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497316, "dur": 21, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497338, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497339, "dur": 30, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497373, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497375, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497419, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497421, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497461, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497464, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497516, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497563, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497565, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497669, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497708, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497825, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497858, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497892, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497894, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497929, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497931, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211497968, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498009, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498011, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498068, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498207, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498208, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498240, "dur": 141, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498386, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211498414, "dur": 159438, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211657863, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211657867, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211657933, "dur": 3895, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211661832, "dur": 37227, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211699068, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211699071, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305211699093, "dur": 766268, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212465369, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212465372, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212465471, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212465476, "dur": 604, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212466092, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212466097, "dur": 826, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212466927, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212466931, "dur": 69677, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212536618, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212536621, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212536706, "dur": 17, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212536724, "dur": 5478, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212542210, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212542213, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212542235, "dur": 23, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212542260, "dur": 14213, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212556480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212556483, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212556505, "dur": 4285, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212560796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212560799, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212560891, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212560895, "dur": 1485, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212562386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212562387, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212562422, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212562441, "dur": 98213, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212660662, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212660665, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212660765, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305212660769, "dur": 953142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213613922, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213613926, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213613984, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213613988, "dur": 1927, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213615920, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213615923, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213615998, "dur": 29, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213616029, "dur": 701, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213616734, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213616736, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213616828, "dur": 430, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753305213617260, "dur": 9566, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21312, "tid": 9640, "ts": 1753305213637309, "dur": 862, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21312, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21312, "tid": 8589934592, "ts": 1753305211378883, "dur": 107291, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21312, "tid": 8589934592, "ts": 1753305211486177, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21312, "tid": 8589934592, "ts": 1753305211486183, "dur": 1120, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21312, "tid": 9640, "ts": 1753305213638173, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21312, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21312, "tid": 4294967296, "ts": 1753305211176631, "dur": 2450955, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753305211180104, "dur": 8289, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753305213627599, "dur": 4133, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753305213629331, "dur": 73, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753305213631788, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21312, "tid": 9640, "ts": 1753305213638178, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753305211200793, "dur": 1787, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305211202589, "dur": 631, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305211203329, "dur": 57, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753305211203387, "dur": 520, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305211204503, "dur": 178812, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753305211384075, "dur": 2810, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753305211386926, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753305211387031, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753305211387296, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753305211387685, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753305211393805, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654656162358534522.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753305211399369, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753305211203924, "dur": 195819, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305211399754, "dur": 2216816, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305213616571, "dur": 213, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305213616784, "dur": 63, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305213616877, "dur": 183, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305213617289, "dur": 1918, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753305211203972, "dur": 195793, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211399793, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211399882, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211399941, "dur": 992, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1753305211399872, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753305211400935, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211401001, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_C7611EE8A5C55055.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753305211401052, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211401163, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211401547, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211401731, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1753305211401919, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753305211402045, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211402256, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211402570, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211402652, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211402741, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753305211402862, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211402916, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753305211403021, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211405494, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\SimpleJSON.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753305211406275, "dur": 783, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\ProjectGeneration\\GUIDProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753305211404555, "dur": 2982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211407537, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211409357, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211411404, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Serialization\\MultiJsonInternal.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753305211410670, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211411944, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211413437, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211415241, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211416647, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\GetDictionaryItem.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753305211416150, "dur": 2581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211418731, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211419963, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211421384, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211423011, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211424283, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211425443, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211425772, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211426570, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211427168, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753305211427291, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211427626, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211428386, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211427496, "dur": 1312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753305211428809, "dur": 1118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211429972, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753305211430091, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753305211430483, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211430599, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211431566, "dur": 56787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211489062, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211489142, "dur": 1243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211492638, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211492747, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211493013, "dur": 617, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211488355, "dur": 5325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753305211493681, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211493801, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211493786, "dur": 1953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753305211495744, "dur": 1429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211497224, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211497224, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753305211497348, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211497489, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211497781, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211497918, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305211498032, "dur": 1063092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753305212561126, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753305212561125, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753305212561324, "dur": 1640, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753305212562967, "dur": 1053588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211204050, "dur": 195880, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211399962, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211400055, "dur": 656, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1753305211399946, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211400839, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211400986, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211400984, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_2FC980318CB13784.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211401052, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211401221, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211401220, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211401387, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211401386, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211401589, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211401720, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211401810, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211402073, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402129, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402210, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402267, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402402, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402504, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402565, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402745, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402853, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211402970, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211403029, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211403129, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211403232, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211403577, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211403655, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211404117, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211404500, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211404663, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211404731, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211404958, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211405089, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211405146, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211405430, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211405754, "dur": 541, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211406528, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211406934, "dur": 339, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211407274, "dur": 298, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211407576, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211407638, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211407701, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211407766, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211407829, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211408111, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211408206, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211408361, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211408436, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211408506, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211408897, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211409101, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211409424, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211409482, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211409637, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211409786, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211410010, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211410076, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211410131, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211410363, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211410539, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211410826, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211410897, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211411053, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211411163, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211411396, "dur": 523, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211411922, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211412012, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211412229, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211412405, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211412528, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIBehaviour.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211412654, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211412845, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413230, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413290, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413414, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413564, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413622, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413688, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\RectangularVertexClipper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413744, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211413984, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211414055, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211414110, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211414494, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211414626, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211414686, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211414780, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ILayoutElement.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211415195, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211415376, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskableGraphic.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211415583, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211415857, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211416088, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Misc.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211416455, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211416690, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753305211417053, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211417331, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211401910, "dur": 15502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211417413, "dur": 952, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211418425, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211419354, "dur": 1641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211420995, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211422105, "dur": 2162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211424267, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211425717, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211426571, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211427057, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211427630, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211428385, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211427629, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211428444, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211428654, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211428771, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211428890, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211428945, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211429151, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211429224, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211429833, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211429982, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211430047, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211429210, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211430460, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211430600, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211430734, "dur": 1250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211431984, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211432130, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211432239, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211432676, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211432771, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753305211432863, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211433095, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211433191, "dur": 55153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211490095, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.HttpListener.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753305211488347, "dur": 2974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211491321, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211491716, "dur": 1941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753305211493658, "dur": 3240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211496905, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211497505, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211497580, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211497946, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211498297, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753305211498513, "dur": 2118033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211203993, "dur": 195798, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211399808, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211399867, "dur": 625, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1753305211399797, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753305211400919, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211401055, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211401110, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211401110, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_065B772FA1729652.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753305211401229, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211401428, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211401614, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211401695, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211401784, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211401851, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211402215, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211402377, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211402654, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211402766, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753305211402910, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211403002, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211403066, "dur": 2373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211406180, "dur": 809, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\CurvesOwner\\ICurvesOwnerInspectorWrapper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753305211405440, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211407392, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211409310, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211410539, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211411896, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211413272, "dur": 2103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211415443, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Subtract.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753305211415375, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211416864, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211418659, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211420546, "dur": 2124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211422670, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211424024, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211425733, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211426566, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211427067, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753305211427509, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753305211428241, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211428421, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211428481, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753305211428635, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211428972, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211429063, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211429389, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211429688, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Analytics\\TimelineAnalytics.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753305211428690, "dur": 1583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753305211430273, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211430514, "dur": 1029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211431544, "dur": 2025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211433571, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753305211433681, "dur": 53121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211486803, "dur": 2004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753305211488809, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211489065, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Common.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211489333, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211489524, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211490955, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211492290, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211489064, "dur": 3770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753305211492835, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211493233, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211495475, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753305211492962, "dur": 3328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753305211496290, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753305211496396, "dur": 2207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753305211498684, "dur": 2117883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211203997, "dur": 195802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211399819, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753305211399879, "dur": 1086, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1753305211399808, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753305211400967, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211401070, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753305211401068, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B8A7170076CF7080.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753305211401160, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211401356, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211401498, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211401747, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211401833, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211401886, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753305211402030, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211402213, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211402281, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211402537, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211402601, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211404548, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Assets\\TextMesh Pro\\Examples & Extras\\Scripts\\Benchmark01.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753305211403014, "dur": 2271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211405686, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\TimelineClipGroup.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753305211405285, "dur": 2016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211407301, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211409011, "dur": 2053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211411389, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Descriptors\\PassDescriptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753305211411065, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211412164, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211413397, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211414310, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211415333, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211417027, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211418138, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211419497, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211421066, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211423004, "dur": 2253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211425258, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211426241, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211426560, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211427068, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753305211427342, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753305211427801, "dur": 1150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211428999, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211429538, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753305211429654, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753305211429178, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753305211430068, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211430202, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211431541, "dur": 1238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211432781, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753305211432888, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211432973, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753305211433330, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211433389, "dur": 53428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211489179, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753305211486819, "dur": 2881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753305211489701, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211492463, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753305211490020, "dur": 3225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753305211493245, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211494188, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753305211493785, "dur": 3089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753305211496874, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497041, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497109, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497196, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497257, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497453, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497711, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497800, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211497963, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753305211498647, "dur": 2117925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211204050, "dur": 195894, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211399990, "dur": 1054, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1753305211399950, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77AE899CA5593464.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211401045, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211401243, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211401242, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_9AAEFD6A746FD785.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211401360, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211401448, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211401578, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211401576, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_E2D6CB2A6174372D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211401732, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753305211401791, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211401928, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211402107, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211402217, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211402299, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211402551, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211402773, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211402879, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211402945, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753305211402999, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211403058, "dur": 2022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211405081, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211406675, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SubMesh_Editor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753305211405849, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211407366, "dur": 2183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211409549, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211411005, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211412597, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211413952, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211415612, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211416098, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211417340, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211418828, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211420843, "dur": 2261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211423105, "dur": 1685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211424790, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211425706, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211426575, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211427062, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211427256, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211427473, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211428125, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211427442, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753305211428317, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211428645, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211428821, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211428895, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211428972, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211429224, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211429388, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753305211430131, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211430310, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753305211430471, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211430534, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753305211430968, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211431254, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211431548, "dur": 55273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211489539, "dur": 449, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211486852, "dur": 3626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753305211490479, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211491187, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211492249, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211492616, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.ApiExplorer.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211490826, "dur": 3822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753305211494652, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211495869, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753305211494898, "dur": 2866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753305211497765, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211497953, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211498539, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753305211498600, "dur": 2117961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211204137, "dur": 195848, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211400027, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753305211400147, "dur": 776, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1753305211400014, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3AC0D0C527F5439B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753305211400925, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211401046, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753305211401044, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8837211AE3CE1746.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753305211401134, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211401219, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753305211401218, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F72D29FA518CE2F1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753305211401403, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211401532, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211401652, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211401825, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211401994, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211402252, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211402682, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211402736, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753305211403045, "dur": 2047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211405626, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Tooltip.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753305211405092, "dur": 2347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211407440, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211409217, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211409767, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211410984, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211412473, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211413825, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211415128, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211416886, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211417782, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211419770, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\UnsafeRingQueue.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753305211419377, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211421272, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211422609, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211423385, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211424538, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211425547, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211425723, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211426568, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211427044, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753305211427134, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211427431, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753305211427214, "dur": 1547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753305211428762, "dur": 1450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211430224, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211430295, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753305211430483, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753305211431527, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211431631, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753305211431721, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753305211432037, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211432156, "dur": 54635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211488580, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753305211488708, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753305211486793, "dur": 2208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753305211489002, "dur": 1331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211490344, "dur": 3295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753305211493640, "dur": 1090, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211494736, "dur": 2882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753305211497619, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211498028, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753305211498102, "dur": 2118461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211203980, "dur": 195801, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211399794, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211399943, "dur": 1042, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1753305211399891, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_63198FADDC2DE5CF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753305211400987, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211401235, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211401455, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211401515, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211401612, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211401672, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211401812, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211401971, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402039, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402115, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402213, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402300, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402404, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402563, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402652, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402707, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753305211402759, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211402969, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211403039, "dur": 1998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211405758, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\TrackGui\\TimelineGroupGUI.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753305211405037, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211406502, "dur": 1960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211408463, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211409885, "dur": 2727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211412613, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211413824, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211414831, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211416527, "dur": 2177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211418704, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211420254, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211421607, "dur": 2101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211423709, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211425026, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211426184, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211426600, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211427131, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753305211427473, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211427425, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753305211428387, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211428752, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753305211428921, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211429180, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211428986, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753305211429713, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211429833, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211430208, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211431552, "dur": 55253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211486868, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211488645, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211489043, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211486809, "dur": 3574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753305211490384, "dur": 1118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211491554, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211491721, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211494545, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753305211491510, "dur": 3645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753305211495156, "dur": 743, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211495911, "dur": 2484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753305211498396, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753305211498578, "dur": 2117981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211204012, "dur": 195818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211399888, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211399970, "dur": 1127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1753305211399860, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211401099, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211401191, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211401190, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_7406CA3F32B5E22E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211401249, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211401379, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211401378, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211401471, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211401531, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211401656, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211401730, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753305211401819, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211401894, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211401992, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211402263, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211402736, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211402852, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211402940, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211403018, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211403163, "dur": 1718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211405573, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\TimelineKeyboardNavigation.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753305211404881, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211407310, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211409404, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211410164, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211411931, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211413133, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211414400, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211415743, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211418126, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211419674, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211421100, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211422346, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211423600, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211425148, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211426226, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211426574, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211427278, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211427469, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753305211428227, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211428509, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211428574, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211428924, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211429173, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211429244, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753305211429889, "dur": 1559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211431539, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211431651, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753305211431957, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211432069, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211432199, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753305211432618, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211432708, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753305211432811, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753305211433157, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211433282, "dur": 55047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211490509, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211488341, "dur": 2994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753305211491336, "dur": 878, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211492230, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211492588, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211492765, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211493077, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211493205, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211494961, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211495203, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211495392, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753305211492224, "dur": 3780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753305211496005, "dur": 1607, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211497620, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211497696, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211497786, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211497850, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211497966, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753305211498703, "dur": 2117845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211204039, "dur": 195825, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211399893, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211399968, "dur": 1164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1753305211399880, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753305211401134, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211401234, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211401233, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64C5CD55CF2E68A6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753305211401551, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211401649, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211402108, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211402194, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211402254, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211402309, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211402730, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211402780, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1410676725171572189.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753305211402951, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753305211403010, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211403068, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211405739, "dur": 624, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\SequenceContext.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753305211404715, "dur": 2173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211406888, "dur": 2187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211409076, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211410212, "dur": 2428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211412641, "dur": 1954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211414595, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211417152, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SelectOnFlow.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753305211416042, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211417693, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211419416, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211420805, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211422182, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211423263, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211425011, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211426238, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211426579, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211427039, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753305211427167, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211427473, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211427307, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753305211428366, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211428602, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211428729, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753305211428877, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211429062, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211429224, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211428968, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753305211429655, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211429895, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211429959, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753305211430147, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753305211430582, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211430715, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753305211430855, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753305211431203, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211431332, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211431560, "dur": 55251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211488661, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211486816, "dur": 3093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753305211489910, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211490301, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211491228, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753305211490047, "dur": 3413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753305211493461, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211494026, "dur": 2929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753305211496955, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497106, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497190, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497320, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497459, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497520, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497577, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497635, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497718, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211497788, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211498028, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753305211498097, "dur": 2118453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211204067, "dur": 195890, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211400006, "dur": 1131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1753305211399964, "dur": 1174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_71CF6729598465FE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753305211401139, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211401263, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211401262, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753305211401378, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211401574, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753305211401806, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211401878, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211402306, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211402574, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211402755, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211402925, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211403043, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211403160, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211403468, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211403590, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211403749, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211403808, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211403868, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211404306, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211404429, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211404730, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211404919, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211404996, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211405111, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211405404, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211405746, "dur": 501, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211406248, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211406784, "dur": 706, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211407633, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211407945, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211408201, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211408437, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211408507, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211408560, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211408617, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211409004, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211409303, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211409795, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211410069, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211410228, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211410526, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211410639, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211410791, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211411016, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211411069, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211411476, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211411844, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211412050, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211412446, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211412560, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211412696, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211412865, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211413183, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211413408, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\WorkItemFactory.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211413539, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestExtensions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211413647, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211413745, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\UnityTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211413887, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayModeRunnerCallback.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211414008, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211414183, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211414356, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211414644, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211415025, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211415291, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211415480, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211415650, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211415963, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\ScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211416079, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211416150, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211416405, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPrebuildSceneSetup.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211416461, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211416515, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211416790, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\QuaternionEqualityComparer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211416990, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Utils.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211417343, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211401686, "dur": 15738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211417425, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211417561, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753305211417855, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211418325, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211418522, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211418687, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211418786, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211418984, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211419126, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211419223, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211419540, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211420107, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211420317, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211420469, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211420941, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211421220, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211421425, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211421534, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211421647, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211421865, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211422212, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211422335, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211422530, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211422944, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423327, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423457, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423511, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423569, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423724, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423858, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423911, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211423962, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211424020, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211424436, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211424632, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211424825, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211425107, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211425222, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211425500, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211425778, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211425875, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211425935, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211425994, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Analytics\\AnalyticsReporter.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211426048, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Analytics\\AnalyticsTestCallback.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211426102, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Analytics\\RunFinishedData.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211426211, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\ITestRunnerApi.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753305211417671, "dur": 8776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211426449, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211426594, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753305211426668, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211427039, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753305211427144, "dur": 624, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211428013, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211427776, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211428730, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211428921, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753305211429225, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211429637, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211429211, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211429808, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211429916, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1753305211430507, "dur": 163, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211431272, "dur": 49493, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1753305211488675, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211486789, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211488953, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211489100, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753305211489080, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211491346, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211491559, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211493456, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211493862, "dur": 2018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753305211495881, "dur": 925, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211496857, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211497040, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211497733, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211497981, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753305211498774, "dur": 2117818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211204128, "dur": 195838, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211400012, "dur": 500, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1753305211399972, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7F06B267F1BFA456.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211400806, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211400890, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753305211400889, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F44FCBA18E1E2303.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211400977, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211401059, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753305211401057, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A857FD1C4743904D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211401131, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211401911, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211402179, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211402595, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211402937, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3818794330681902346.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753305211402997, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211403060, "dur": 1833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211405747, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\CustomTrackDrawerAttribute.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753305211404894, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211406486, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211407775, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211409134, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211410359, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211411821, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211413489, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211417128, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Ports\\IUnitPort.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753305211414999, "dur": 2808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211417807, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211419156, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211420378, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211421860, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211423120, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211424442, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211425365, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211426106, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211426555, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211427067, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211427173, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211428311, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753305211428935, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\Views\\IncomingChanges\\Gluon\\IncomingChangesTreeView.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753305211429115, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\Views\\PendingChanges\\Dialogs\\EmptyCheckinMessageDialog.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753305211427790, "dur": 1501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211429291, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211429560, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211430296, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211430437, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211431475, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211431644, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211431727, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211431785, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211432304, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211432403, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211432496, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211432926, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211433106, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211433166, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211433320, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211433474, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211433566, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753305211433660, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211434186, "dur": 224220, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211666175, "dur": 33186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753305211665931, "dur": 33513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753305211699446, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305211699610, "dur": 763994, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753305211699608, "dur": 765226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753305212465705, "dur": 130, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753305212465897, "dur": 71267, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753305212556898, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1753305212556898, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1753305212557030, "dur": 1059572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211204147, "dur": 195866, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211400059, "dur": 592, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1753305211400018, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_38FFBE07FFE62337.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211400653, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211400737, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211400735, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211400816, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211400882, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211400880, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211401071, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211401069, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_609DF0C9F4BC7F1C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211401144, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211401250, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211401249, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_ADDD82C2A90EA988.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211401450, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211401550, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211401846, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211402088, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211402203, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211402765, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211403034, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211405605, "dur": 1204, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Signals\\SignalEmitterEditor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753305211405126, "dur": 2344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211407471, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211411381, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\VFXGraph\\VFXURPLitPlanarPrimitiveOutput.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753305211409024, "dur": 2977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211412001, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211412520, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211413614, "dur": 2020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211415634, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211417390, "dur": 1991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211419382, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211420641, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211422442, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211423774, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211425263, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211426319, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211426585, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211427089, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211427351, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211427419, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211427556, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211427631, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211427630, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753305211428363, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211428524, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211428947, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753305211429064, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211429062, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753305211429611, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211429790, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211430212, "dur": 1350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211431563, "dur": 55231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211486796, "dur": 1906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753305211488703, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211489054, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211489143, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211488880, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753305211490679, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211490785, "dur": 2129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753305211492914, "dur": 904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211494966, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Xml.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211495200, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\msquic.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211495384, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211493825, "dur": 3058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753305211496884, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211497071, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211497195, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211497546, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211497545, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753305211497947, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211498480, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753305211498564, "dur": 2118045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211204171, "dur": 195847, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211400064, "dur": 646, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1753305211400023, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753305211400986, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211400985, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EC1D9C7602E10CB1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753305211401066, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211401188, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211401187, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E37D1A982AA61309.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753305211401366, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211401498, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211401784, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211401886, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211401961, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211402223, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211402303, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211402537, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211402616, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211403001, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211405527, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\GenericContext.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753305211404390, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211406187, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211407481, "dur": 2178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211409659, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\StateUnit.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753305211409659, "dur": 1543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211411430, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Data\\FieldCondition.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753305211411202, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211412773, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211413599, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211415476, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211417604, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211418812, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211419854, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211421393, "dur": 2152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211423546, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211425068, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211426310, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211426569, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211427050, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753305211427209, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211427473, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211427385, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753305211427972, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211428282, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211428859, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753305211428975, "dur": 823, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211429801, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753305211430208, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211430301, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211430387, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211431538, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211432408, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753305211432514, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753305211432759, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211432887, "dur": 53912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211486801, "dur": 2057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753305211488859, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211490360, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Core.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211489191, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753305211491381, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211493681, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211491597, "dur": 2583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753305211494180, "dur": 734, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211495252, "dur": 1128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211496882, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211497823, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211497965, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753305211494920, "dur": 3268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753305211498189, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211498251, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753305211498397, "dur": 2118160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211204188, "dur": 195839, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211400087, "dur": 736, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1753305211400033, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753305211400825, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211400925, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211400923, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_21357EA18D8E171B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753305211401073, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211401072, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_387623EB1508B7EB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753305211401157, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211401390, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211401389, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753305211401628, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211402032, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211402225, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211402304, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211402723, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211402892, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211403029, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211405499, "dur": 910, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\TimelineWindow_PreviewPlayMode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753305211404609, "dur": 2272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211406881, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211408663, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211409320, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211411116, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Interface\\IRequiresData.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753305211410826, "dur": 2648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211413475, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211414563, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211416445, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211416988, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211418549, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211420163, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211421235, "dur": 1923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211423158, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211424650, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211425827, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211426562, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211427200, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753305211427385, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211427474, "dur": 905, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211428386, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211428935, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\UnityInstallation.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753305211427440, "dur": 1705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753305211429146, "dur": 869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211430183, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211431557, "dur": 55232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211486790, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753305211488915, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211490807, "dur": 2223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211493039, "dur": 423, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211493617, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211489147, "dur": 4901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753305211494049, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211496358, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753305211494689, "dur": 2688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753305211497377, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211497520, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211497843, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211497988, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753305211498930, "dur": 2117681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211204209, "dur": 195826, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211400075, "dur": 637, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1753305211400039, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_826CA845B2B614E1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753305211400713, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211400790, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211400788, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D0034867E905C483.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753305211400850, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211400998, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211400996, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FDF3153BFA104355.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753305211401126, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211401125, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4423571CA8F0F05E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753305211401254, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211401254, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_F27749E0A6B65183.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753305211401511, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211401586, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211401645, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211401766, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753305211401909, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211402117, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211402283, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211402361, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211402648, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211402842, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211402950, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753305211403040, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211404777, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-2-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211405660, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211404263, "dur": 2390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211406653, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211407614, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211408986, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211411166, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Events\\GlobalMessageListenerEditor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753305211410406, "dur": 2267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211412673, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211413927, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211415515, "dur": 2812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211418327, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211419512, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211421683, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211423299, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211424726, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211426205, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211426645, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211427063, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753305211427174, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211427627, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211427303, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753305211428248, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211428462, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211428949, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753305211429115, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211429541, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211429075, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753305211429875, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211429956, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211430144, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211430327, "dur": 1220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211431547, "dur": 55249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211486846, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211487419, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211488018, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211488483, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211488761, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Abstractions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211489101, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211486798, "dur": 3533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753305211490332, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211491477, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211490840, "dur": 2808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753305211493649, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211494044, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211494619, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211495946, "dur": 436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211496887, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211497217, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211493770, "dur": 3617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753305211497388, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211497564, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211497635, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211497773, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211497846, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211498004, "dur": 167935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305211665943, "dur": 797806, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305211665942, "dur": 799306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753305212466426, "dur": 132, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753305212466570, "dur": 76207, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753305212561120, "dur": 1053249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305212561119, "dur": 1053253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753305213614398, "dur": 2078, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211204232, "dur": 195840, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211400124, "dur": 725, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1753305211400076, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4F40831771D8FBF5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753305211400892, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_277964AD70D626E0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753305211401022, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211401085, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211401084, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E81E4251EABAE467.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753305211401221, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211401367, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211401706, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211401853, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211401986, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211402220, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211402425, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211402755, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211402812, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753305211402948, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753305211403014, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211403090, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211404588, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211405638, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Attributes\\TimelineShortcutAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753305211405638, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211407255, "dur": 2150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211409405, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211411391, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\CustomRenderTexture\\CustomTextureSize.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753305211410885, "dur": 2061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211412947, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211413736, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211415298, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211417190, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211418410, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211419752, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211420881, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211422585, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211424192, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211425772, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211426595, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211427047, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753305211427260, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211427474, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211427626, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211427886, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211428386, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Textures\\BufferedRTHandleSystem.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753305211427363, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753305211428616, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211428767, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753305211429064, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211428890, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753305211429360, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211429688, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211430221, "dur": 1332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211431553, "dur": 55247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211486801, "dur": 2073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753305211488874, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211490358, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211490754, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211489016, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753305211491367, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211491447, "dur": 2110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753305211493557, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211494095, "dur": 819, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305211493629, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753305211496576, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211496990, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497055, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497130, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497244, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497447, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497531, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497616, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497702, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497801, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211497863, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305211498007, "dur": 1058895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753305212556905, "dur": 104109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305212556904, "dur": 104112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753305212661074, "dur": 955500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211204259, "dur": 195818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211400120, "dur": 718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1753305211400081, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_4245840F5C4641AB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753305211400840, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211400935, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753305211400933, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753305211401022, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211401132, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753305211401114, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0F9F7C5D1B98DB2C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753305211401213, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211401791, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753305211401857, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211401916, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1753305211402173, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211402341, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211402718, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753305211402912, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8336928665352126947.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753305211403056, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211403989, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211405597, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\BuiltInCurvePresets.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753305211405597, "dur": 2325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211407923, "dur": 2039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211410001, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_4_3.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753305211409963, "dur": 2198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211412161, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211413669, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211414662, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211416041, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211417673, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211419084, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211420052, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211421496, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211422205, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211423599, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211424723, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211426061, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211426557, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211427064, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753305211427176, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211428014, "dur": 387, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753305211427684, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753305211428658, "dur": 905, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211429572, "dur": 825, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211430400, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753305211430538, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753305211431009, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211431100, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211431157, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211431535, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753305211431674, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753305211432063, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211432163, "dur": 56152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211488326, "dur": 7323, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211496351, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753305211497010, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-filesystem-l1-1-0.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753305211497266, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\dbgshim.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753305211495650, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753305211498552, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753305211498637, "dur": 2117978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211204278, "dur": 195808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211400126, "dur": 768, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1753305211400091, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753305211400895, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211400956, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753305211401046, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B283CA44A21F1336.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753305211401143, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211401142, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_EA305F3817072CC7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753305211401247, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211401513, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211401822, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211402026, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211402138, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753305211402231, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211402490, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211402996, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211403107, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211405219, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\VisualStudioEditor.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753305211404498, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211406066, "dur": 1843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211407910, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211409138, "dur": 2290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211411429, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211413018, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211414198, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211415597, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211417481, "dur": 1881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211419362, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211420676, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211421483, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211422508, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211423702, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211424479, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211425710, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211426554, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211427052, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753305211427323, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211427629, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211427197, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753305211428270, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211428665, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211428934, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211428411, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753305211429103, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211429340, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211429555, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211430230, "dur": 1309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211431539, "dur": 1173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211432713, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753305211432838, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753305211433128, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211433292, "dur": 55482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211488785, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753305211490969, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211491056, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753305211493279, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211493615, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211494898, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211493359, "dur": 2547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753305211495907, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211496369, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211497012, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211497265, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211497822, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Rewrite.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753305211495986, "dur": 2833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753305211498820, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753305211498922, "dur": 2117682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211204297, "dur": 195810, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211400171, "dur": 757, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1753305211400114, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753305211401023, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211401208, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211401207, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3F15606BE1109C0F.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753305211401405, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211401404, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753305211401729, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211401786, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753305211402035, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211402233, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211402373, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211402623, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211402684, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753305211402941, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753305211403054, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211404120, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211406018, "dur": 796, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\AddDelete\\IAddDeleteItemMode.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753305211405345, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211406978, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211408299, "dur": 1898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211410198, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211412042, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211413412, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211415230, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211417056, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Connections\\UnitConnection.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753305211416514, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211418064, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211419983, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211421749, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211423689, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211424991, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211426152, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211426556, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211427049, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753305211427174, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211427475, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211428011, "dur": 391, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.ref.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211427241, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753305211428453, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211428547, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211428784, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753305211429373, "dur": 1221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211430597, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753305211430944, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211431115, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753305211431258, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753305211431790, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211431937, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753305211432030, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753305211432288, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211432392, "dur": 56396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211489101, "dur": 1305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211490753, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211492651, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211488789, "dur": 4594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753305211493384, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211493681, "dur": 427, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211493473, "dur": 2796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753305211496270, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211496883, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211497128, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211497822, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.WebSockets.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753305211496355, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753305211498676, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753305211498771, "dur": 2117824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211204317, "dur": 195799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211400155, "dur": 705, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1753305211400117, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753305211400916, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211400915, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753305211401025, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211401226, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211401355, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211401525, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211401614, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211401705, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211401846, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211401964, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753305211402113, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211402269, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211402757, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654656162358534522.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753305211403094, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211404087, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211407146, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_PostBuildProcessHandler.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753305211405999, "dur": 2211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211408210, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211409308, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211411241, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211412999, "dur": 2054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211415053, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211417155, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Widgets\\WidgetProvider.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753305211416731, "dur": 1908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211418639, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211419974, "dur": 1970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211421945, "dur": 2164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211424109, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211424973, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211425967, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211426553, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211427055, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753305211427565, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211427658, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753305211428216, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211428421, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211428687, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211428948, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753305211429460, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211429546, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211429806, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211429545, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753305211430180, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211430317, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211430685, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211431537, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753305211431617, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211431672, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753305211432024, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211432108, "dur": 54677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211489304, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211486796, "dur": 2677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753305211489473, "dur": 2032, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211493784, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211491514, "dur": 3090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753305211494605, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211494899, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211495242, "dur": 723, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211496723, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Core.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211497012, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211497543, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753305211494694, "dur": 3705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753305211498400, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753305211498496, "dur": 2118057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753305213623725, "dur": 3336, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21312, "tid": 9640, "ts": 1753305213638943, "dur": 179952, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21312, "tid": 9640, "ts": 1753305213818981, "dur": 2060, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21312, "tid": 9640, "ts": 1753305213635656, "dur": 185999, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}